{"extension": {"name": "Roo Code", "description": "Cały zespół programistów AI w Twoim edytorze."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "<PERSON><PERSON><PERSON>, {{name}}! <PERSON><PERSON> {{count}} powiadomień.", "items": {"zero": "Brak elementów", "one": "Jeden element", "other": "{{count}} elementów"}, "confirmation": {"reset_state": "Czy na pewno chcesz zresetować wszystkie stany i tajne magazyny w rozszerzeniu? Tej operacji nie można cofnąć.", "delete_config_profile": "<PERSON>zy na pewno chcesz usunąć ten profil konfiguracyjny?", "delete_custom_mode_with_rules": "Czy na pewno chcesz usunąć ten tryb {scope}?\n\nSpowoduje to również usunięcie powiązanego folderu reguł pod adresem:\n{rulesFolderPath}"}, "errors": {"invalid_data_uri": "Nieprawidłowy format URI danych", "error_copying_image": "Błąd kopiowania obrazu: {{errorMessage}}", "error_saving_image": "Błąd zapisywania obrazu: {{errorMessage}}", "error_opening_image": "Błąd otwierania obrazu: {{error}}", "could_not_open_file": "<PERSON>e można otworzyć pliku: {{errorMessage}}", "could_not_open_file_generic": "Nie można otworzyć pliku!", "checkpoint_timeout": "Upłynął limit czasu podczas próby przywrócenia punktu kontrolnego.", "checkpoint_failed": "Nie udało się przywrócić punktu kontrolnego.", "no_workspace": "Najpierw otwórz folder projektu", "update_support_prompt": "Nie udało się zaktualizować komunikatu wsparcia", "reset_support_prompt": "<PERSON>e udało się zresetować komunikatu wsparcia", "enhance_prompt": "<PERSON>e udało się ulepszyć komunikatu", "get_system_prompt": "Nie udało się pobrać komunikatu systemowego", "search_commits": "<PERSON>e udało się wyszukać commitów", "save_api_config": "Nie udało się zapisać konfiguracji API", "create_api_config": "Nie udało się utworzyć konfiguracji API", "rename_api_config": "Nie udało się zmienić nazwy konfiguracji API", "load_api_config": "Nie udało się załadować konfiguracji API", "delete_api_config": "<PERSON>e udało się usunąć konfiguracji API", "list_api_config": "Nie udało się pobrać listy konfiguracji API", "update_server_timeout": "<PERSON>e udało się zaktualizować limitu czasu serwera", "hmr_not_running": "Lokalny serwer deweloperski nie jest urucho<PERSON>ny, HMR nie bę<PERSON><PERSON>. Uruchom 'npm run dev' przed uruchomieniem rozszerzenia, aby włączyć HMR.", "retrieve_current_mode": "Błąd podczas pobierania bieżącego trybu ze stanu.", "failed_delete_repo": "Nie udało się usunąć powiązanego repozytorium lub gałęzi pomocniczej: {{error}}", "failed_remove_directory": "<PERSON><PERSON> udało się usunąć katalogu zadania: {{error}}", "custom_storage_path_unusable": "Niestandardowa ścieżka przechowywania \"{{path}}\" nie jest u<PERSON>, zostanie użyta domyślna ścieżka", "cannot_access_path": "<PERSON>e można uzyskać dostępu do ścieżki {{path}}: {{error}}", "settings_import_failed": "<PERSON><PERSON> udało się zaimportować ustawień: {{error}}.", "mistake_limit_guidance": "To może wskazywać na błąd w procesie myślowym modelu lub niezdoln<PERSON> do prawidłowego użycia narzędzia, co można złagodzić poprzez wskazówki użytkownika (np. \"Spróbuj podzielić zadanie na mniejsze kroki\").", "violated_organization_allowlist": "Nie udało się uruchomić zadania: bież<PERSON><PERSON> profil nie jest kompatybilny z ustawieniami Twojej organizacji", "condense_failed": "<PERSON><PERSON> udało się skondensować kontekstu", "condense_not_enough_messages": "Za mało wiadomości do skondensowania kontekstu", "condensed_recently": "Kontekst został niedawno skondensowany; pomijanie tej próby", "condense_handler_invalid": "Nieprawidłowy handler API do kondensowania kontekstu", "condense_context_grew": "Rozmiar kontekstu wzrósł podczas kondensacji; pomijanie tej próby", "url_timeout": "Strona internetowa ładowała się zbyt długo (timeout). <PERSON><PERSON><PERSON> to być spowodowane wolnym połączeniem, c<PERSON><PERSON><PERSON><PERSON><PERSON> stroną lub tymczasową niedostępnością. <PERSON><PERSON><PERSON><PERSON> spr<PERSON>bować ponownie później lub spraw<PERSON>, czy URL jest poprawny.", "url_not_found": "Nie można znaleźć adresu strony internetowej. Sprawdź, czy URL jest poprawny i spróbuj ponownie.", "no_internet": "Brak połączenia z internetem. Sprawdź połączenie sieciowe i spróbuj ponownie.", "url_forbidden": "Dostęp do tej strony internetowej jest zabroniony. Strona może blokować automatyczny dostęp lub wymagać uwierzytelnienia.", "url_page_not_found": "Strona nie została znaleziona. Sprawdź, czy URL jest poprawny.", "url_fetch_failed": "Błąd pobierania zawartości URL: {{error}}", "url_fetch_error_with_url": "Błąd pobierania zawartości dla {{url}}: {{error}}", "command_timeout": "Przek<PERSON><PERSON><PERSON> limit czasu wykonania polecenia po {{seconds}} sekundach", "share_task_failed": "Nie udało się udostępnić zadania", "share_no_active_task": "Brak aktywnego zadania do udostępnienia", "share_auth_required": "Wymagana autoryzacja. Z<PERSON><PERSON><PERSON>, aby u<PERSON><PERSON><PERSON><PERSON><PERSON> zadania.", "share_not_enabled": "Udostępnianie zadań nie jest włączone dla tej organizacji.", "share_task_not_found": "Zadanie nie znalezione lub dostęp odmówiony.", "mode_import_failed": "Import trybu nie powiódł się: {{error}}", "delete_rules_folder_failed": "<PERSON>e udało się usunąć folderu reguł: {{rulesFolderPath}}. Błąd: {{error}}", "claudeCode": {"processExited": "Proces Claude Code zakończył się kodem {{exitCode}}.", "errorOutput": "<PERSON><PERSON><PERSON>ście błędu: {{output}}", "processExitedWithError": "Proces Claude Code zakończył się kodem {{exitCode}}. Wyjście błędu: {{output}}", "stoppedWithReason": "<PERSON> zatrzymał się z powodu: {{reason}}", "apiKeyModelPlanMismatch": "Klucze API i plany subskrypcji pozwalają na różne modele. Upewnij się, że wybrany model jest zawarty w twoim planie."}}, "warnings": {"no_terminal_content": "Nie wybrano zawartości terminala", "missing_task_files": "Pliki tego zadania są brakujące. <PERSON><PERSON> ch<PERSON> usunąć je z listy zadań?", "auto_import_failed": "<PERSON>e udało się automatycznie zaimportować ustawień RooCode: {{error}}"}, "info": {"no_changes": "Nie znaleziono zmian.", "clipboard_copy": "Komunikat systemowy został pomyślnie skopiowany do schowka", "history_cleanup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} zadań z brakującymi plikami z historii.", "custom_storage_path_set": "Ustawiono niestandardową ścieżkę przechowywania: {{path}}", "default_storage_path": "Wznowiono używanie domyślnej ścieżki przechowywania", "settings_imported": "Ustawienia zaimportowane pomyślnie.", "auto_import_success": "Ustawienia RooCode zostały automatycznie zaimportowane z {{filename}}", "share_link_copied": "Link udostępniania skopiowany do schowka", "image_copied_to_clipboard": "URI danych obrazu skopiowane do schowka", "image_saved": "<PERSON><PERSON>z zapisany w {{path}}", "organization_share_link_copied": "Link udostępniania organizacji skopiowany do schowka!", "public_share_link_copied": "Publiczny link udostępniania skopiowany do schowka!", "mode_exported": "Tryb '{{mode}}' pomyślnie wyeksportowany", "mode_imported": "Tryb pomyślnie zaimportowany"}, "answers": {"yes": "Tak", "no": "<PERSON><PERSON>", "remove": "Usuń", "keep": "<PERSON><PERSON><PERSON>"}, "buttons": {"save": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>"}, "tasks": {"canceled": "Błąd zadania: Zostało zatrzymane i anulowane przez użytkownika.", "deleted": "Niepowodzenie zadania: Zostało zatrzymane i usunięte przez użytkownika.", "incomplete": "<PERSON><PERSON>nie #{{taskNumber}} (Niekompletne)", "no_messages": "<PERSON><PERSON><PERSON> #{{taskNumber}} (<PERSON><PERSON>)"}, "storage": {"prompt_custom_path": "Wprowadź niestandardową ścieżkę przechowywania dla historii konwersacji lub pozostaw puste, aby użyć lokalizacji domyślnej", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "Wprowadź pełną ścieżkę (np. D:\\RooCodeStorage lub /home/<USER>/storage)", "enter_valid_path": "Wprowadź prawidłową ścieżkę"}, "input": {"task_prompt": "Co ma zrobić Roo?", "task_placeholder": "Wpisz swoje zadanie tutaj"}, "settings": {"providers": {"groqApiKey": "Klucz API Groq", "getGroqApiKey": "Uzyskaj klucz API Groq", "claudeCode": {"pathLabel": "Ścieżka Claude Code", "description": "Opcjonalna ścieżka do Twojego CLI Claude Code. Domyślnie 'claude', jeśli nie ustawi<PERSON>.", "placeholder": "Domyślnie: claude"}}}, "customModes": {"errors": {"yamlParseError": "Nieprawidłowy YAML w pliku .roomodes w linii {{line}}. Sprawdź:\n• Prawidłowe wcięcia (używaj spacji, nie tabulatorów)\n• Pasujące cudzysłowy i nawiasy\n• Prawidłową składnię YAML", "schemaValidationError": "Nieprawidłowy format trybów niestandardowych w .roomodes:\n{{issues}}", "invalidFormat": "Nieprawidłowy format trybów niestandardowych. Upewnij <PERSON>, że twoje ustawienia są zgodne z prawidłowym formatem YAML.", "updateFailed": "Aktualizacja trybu niestandardowego nie powiodła się: {{error}}", "deleteFailed": "Usunięcie trybu niestandardowego nie powiodło się: {{error}}", "resetFailed": "Resetowanie trybów niestandardowych nie powiodło się: {{error}}", "modeNotFound": "Błąd zapisu: <PERSON>b nie został znaleziony", "noWorkspaceForProject": "Nie znaleziono folderu obszaru roboczego dla trybu specyficznego dla projektu"}, "scope": {"project": "projekt", "global": "globalny"}}, "mdm": {"errors": {"cloud_auth_required": "Twoja organizacja wymaga uwierzytelnienia Roo Code Cloud. Z<PERSON><PERSON><PERSON>, aby k<PERSON>.", "organization_mismatch": "Mu<PERSON><PERSON> <PERSON> uwierzytelniony kontem Roo Code Cloud swojej organizacji.", "verification_failed": "Nie można zweryfikować uwierzytelnienia organizacji."}}, "prompts": {"deleteMode": {"title": "Us<PERSON>ń tryb niestandardowy", "description": "Czy na pewno chcesz usunąć ten tryb {{scope}}? Spowoduje to również usunięcie powiązanego folderu z regułami w {{rulesFolderPath}}", "descriptionNoRules": "<PERSON>zy na pewno chcesz usunąć ten tryb niestandardowy?", "confirm": "Usuń"}}, "commands": {"preventCompletionWithOpenTodos": {"description": "Zapobiegaj ukończeniu zadania gdy na liście zadań są nieukończone zadania"}}}