{"unknownError": "Unknown error", "authenticationFailed": "Failed to create embeddings: Authentication failed. Please check your API key.", "failedWithStatus": "Failed to create embeddings after {{attempts}} attempts: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "Failed to create embeddings after {{attempts}} attempts: {{errorMessage}}", "failedMaxAttempts": "Failed to create embeddings after {{attempts}} attempts", "textExceedsTokenLimit": "Text at index {{index}} exceeds maximum token limit ({{itemTokens}} > {{maxTokens}}). Skipping.", "rateLimitRetry": "Rate limit hit, retrying in {{delayMs}}ms (attempt {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "Could not read error body", "requestFailed": "Ollama API request failed with status {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "Invalid response structure from Ollama API: \"embeddings\" array not found or not an array.", "embeddingFailed": "Ollama embedding failed: {{message}}", "serviceNotRunning": "Ollama service is not running at {{baseUrl}}", "serviceUnavailable": "Ollama service is unavailable (status: {{status}})", "modelNotFound": "Ollama model not found: {{modelId}}", "modelNotEmbeddingCapable": "Ollama model is not embedding capable: {{modelId}}", "hostNotFound": "Ollama host not found: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "Unknown error processing file {{filePath}}", "unknownErrorDeletingPoints": "Unknown error deleting points for {{filePath}}", "failedToProcessBatchWithError": "Failed to process batch after {{maxRetries}} attempts: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Failed to connect to Qdrant vector database. Please ensure Qdrant is running and accessible at {{qdrantUrl}}. Error: {{errorMessage}}", "vectorDimensionMismatch": "Failed to update vector index for new model. Please try clearing the index and starting again. Details: {{errorMessage}}"}, "validation": {"authenticationFailed": "Authentication failed. Please check your API key in the settings.", "connectionFailed": "Failed to connect to the embedder service. Please check your connection settings and ensure the service is running.", "modelNotAvailable": "The specified model is not available. Please check your model configuration.", "configurationError": "Invalid embedder configuration. Please review your settings.", "serviceUnavailable": "The embedder service is not available. Please ensure it is running and accessible.", "invalidEndpoint": "Invalid API endpoint. Please check your URL configuration.", "invalidEmbedderConfig": "Invalid embedder configuration. Please check your settings.", "invalidApiKey": "Invalid API key. Please check your API key configuration.", "invalidBaseUrl": "Invalid base URL. Please check your URL configuration.", "invalidModel": "Invalid model. Please check your model configuration.", "invalidResponse": "Invalid response from embedder service. Please check your configuration.", "apiKeyRequired": "API key is required for this embedder", "baseUrlRequired": "Base URL is required for this embedder"}, "serviceFactory": {"openAiConfigMissing": "OpenAI configuration missing for embedder creation", "ollamaConfigMissing": "Ollama configuration missing for embedder creation", "openAiCompatibleConfigMissing": "OpenAI Compatible configuration missing for embedder creation", "geminiConfigMissing": "Gemini configuration missing for embedder creation", "mistralConfigMissing": "Mistral configuration missing for embedder creation", "invalidEmbedderType": "Invalid embedder type configured: {{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "Could not determine vector dimension for model '{{modelId}}' with provider '{{provider}}'. Please ensure the 'Embedding Dimension' is correctly set in the OpenAI-Compatible provider settings.", "vectorDimensionNotDetermined": "Could not determine vector dimension for model '{{modelId}}' with provider '{{provider}}'. Check model profiles or configuration.", "qdrantUrlMissing": "Qdrant URL missing for vector store creation", "codeIndexingNotConfigured": "Cannot create services: Code indexing is not properly configured"}}