{"extension": {"name": "Roo Code", "description": "Ein komplettes Entwicklerteam mit KI in deinem Editor."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "<PERSON><PERSON><PERSON><PERSON>, {{name}}! Du hast {{count}} Benachrichtigungen.", "items": {"zero": "<PERSON><PERSON>", "one": "Ein Element", "other": "{{count}} El<PERSON>e"}, "confirmation": {"reset_state": "Möchtest du wirklich alle Zustände und geheimen Speicher in der Erweiterung zurücksetzen? Dies kann nicht rückgängig gemacht werden.", "delete_config_profile": "Möchtest du dieses Konfigurationsprofil wirklich löschen?", "delete_custom_mode_with_rules": "B<PERSON> du sicher, dass du diesen {scope}-Modus löschen möchtest?\n\n<PERSON><PERSON>ch wird auch der zugehörige Regelordner unter folgender Adresse gelöscht:\n{rulesFolderPath}"}, "errors": {"invalid_data_uri": "Ungültiges Daten-URI-Format", "error_copying_image": "<PERSON><PERSON> beim Ko<PERSON>ren des Bildes: {{errorMessage}}", "error_saving_image": "<PERSON><PERSON> beim Speichern des Bildes: {{errorMessage}}", "error_opening_image": "<PERSON><PERSON> beim Ö<PERSON>nen des Bildes: {{error}}", "could_not_open_file": "<PERSON>i konnte nicht geöffnet werden: {{errorMessage}}", "could_not_open_file_generic": "Datei konnte nicht geöffnet werden!", "checkpoint_timeout": "Zeitüberschreitung beim Versuch, den Checkpoint wiederherzustellen.", "checkpoint_failed": "Fehler beim Wiederherstellen des Checkpoints.", "no_workspace": "Bitte öffne zu<PERSON>t einen Projektordner", "update_support_prompt": "Fehler beim Aktualisieren der Support-Nachricht", "reset_support_prompt": "Fehler beim Zurücksetzen der Support-Nachricht", "enhance_prompt": "Fehler beim Verbessern der Nachricht", "get_system_prompt": "Fehler beim Abrufen der Systemnachricht", "search_commits": "<PERSON><PERSON> beim <PERSON>", "save_api_config": "Fehler beim Speichern der API-Konfiguration", "create_api_config": "Fehler beim Erstellen der API-Konfiguration", "rename_api_config": "Fehler beim Umbenennen der API-Konfiguration", "load_api_config": "Fehler beim Laden der API-Konfiguration", "delete_api_config": "Fehler beim Löschen der API-Konfiguration", "list_api_config": "Fehler beim Abrufen der API-Konfigurationsliste", "update_server_timeout": "Fehler beim Aktualisieren des Server-Timeouts", "hmr_not_running": "Der lokale Entwicklungsserver läuft nicht, HMR wird nicht funktionieren. Bitte führen Sie 'npm run dev' vor dem Start der Erweiterung aus, um HMR zu aktivieren.", "retrieve_current_mode": "Fehler beim Abrufen des aktuellen Modus aus dem Zustand.", "failed_delete_repo": "Fehler beim Löschen des zugehörigen Shadow-Repositorys oder -Zweigs: {{error}}", "failed_remove_directory": "Fehler beim Entfernen des Aufgabenverzeichnisses: {{error}}", "custom_storage_path_unusable": "Benutzerdefinierter Speicherpfad \"{{path}}\" ist nicht verwendbar, Standardpfad wird verwendet", "cannot_access_path": "Zugriff auf Pfad {{path}} nicht möglich: {{error}}", "settings_import_failed": "<PERSON><PERSON> beim Importieren der Einstellungen: {{error}}.", "mistake_limit_guidance": "Dies kann auf einen Fehler im Denkprozess des Modells oder die Unfähigkeit hin<PERSON>sen, ein <PERSON><PERSON> richtig zu verwenden, was durch Benutzerführung behoben werden kann (z.B. \"Versuche, die Aufgabe in kleinere Schritte zu unterteilen\").", "violated_organization_allowlist": "Aufgabe konnte nicht ausgeführt werden: Das aktuelle Profil ist nicht kompatibel mit den Einstellungen deiner Organisation", "condense_failed": "Fehler beim Verdichten des Kontexts", "condense_not_enough_messages": "Nicht genügend Nachrichten zum Verdichten des Kontexts", "condensed_recently": "Kontext wurde kürzlich verdichtet; dieser Versuch wird übersprungen", "condense_handler_invalid": "API-Handler zum Verdichten des Kontexts ist ungültig", "condense_context_grew": "Kontextgröße ist während der Verdichtung gewachsen; dieser Versuch wird übersprungen", "url_timeout": "Die Website hat zu lange zum Laden gebraucht (Timeout). Das könnte an einer langsamen Verbindung, einer schweren Website oder vorübergehender Nichtverfügbarkeit liegen. Du kannst es später nochmal versuchen oder prüfen, ob die URL korrekt ist.", "url_not_found": "Die Website-Adresse konnte nicht gefunden werden. Bitte prüfe, ob die URL korrekt ist und versuche es erneut.", "no_internet": "Keine Internetverbindung. Bitte prüfe deine Netzwerkverbindung und versuche es erneut.", "url_forbidden": "Zugriff auf diese Website ist verboten. Die Seite könnte automatisierten Zugriff blockieren oder eine Authentifizierung erfordern.", "url_page_not_found": "Die Seite wurde nicht gefunden. Bitte prüfe, ob die URL korrekt ist.", "url_fetch_failed": "Fehler beim Abrufen des URL-Inhalts: {{error}}", "url_fetch_error_with_url": "<PERSON><PERSON> beim Abrufen des Inhalts für {{url}}: {{error}}", "command_timeout": "Zeitüberschreitung bei der Befehlsausführung nach {{seconds}} Sekunden", "share_task_failed": "Teilen der Aufgabe fehlgeschlagen. Bitte versuche es erneut.", "share_no_active_task": "Keine aktive Aufgabe zum Teilen", "share_auth_required": "Authentifizierung erforderlich. Bitte melde dich an, um Aufgaben zu teilen.", "share_not_enabled": "Aufgabenfreigabe ist für diese Organisation nicht aktiviert.", "share_task_not_found": "Aufgabe nicht gefunden oder Zugriff verweigert.", "mode_import_failed": "<PERSON><PERSON> beim Importieren des Modus: {{error}}", "delete_rules_folder_failed": "<PERSON><PERSON> beim Löschen des Regelordners: {{rulesFolderPath}}. <PERSON><PERSON>: {{error}}", "claudeCode": {"processExited": "Claude Code Prozess wurde mit Code {{exitCode}} beendet.", "errorOutput": "Fehlerausgabe: {{output}}", "processExitedWithError": "<PERSON> Code Prozess wurde mit Code {{exitCode}} beendet. Fehlerausgabe: {{output}}", "stoppedWithReason": "<PERSON> wurde mit Grund gestoppt: {{reason}}", "apiKeyModelPlanMismatch": "API-Schlüssel und Abonnement-Pläne erlauben verschiedene Modelle. <PERSON><PERSON> sic<PERSON>, dass das ausgewählte Modell in deinem Plan enthalten ist."}}, "warnings": {"no_terminal_content": "Kein Terminal-Inhalt ausgewählt", "missing_task_files": "Die Dateien dieser Aufgabe fehlen. Möchtest du sie aus der Aufgabenliste entfernen?", "auto_import_failed": "Fehler beim automatischen Import der RooCode-Einstellungen: {{error}}"}, "info": {"no_changes": "Keine Änderungen gefunden.", "clipboard_copy": "Systemnachricht erfolgreich in die Zwischenablage kopiert", "history_cleanup": "{{count}} Aufgabe(n) mit fehlenden Dateien aus dem Verlauf bereinigt.", "custom_storage_path_set": "Benutzerdefinierter Speicherpfad festgelegt: {{path}}", "default_storage_path": "Auf Standardspeicherpfad zurückgesetzt", "settings_imported": "Einstellungen erfolgreich importiert.", "auto_import_success": "RooCode-Einstellungen automatisch importiert aus {{filename}}", "share_link_copied": "Share-Link in die Zwischenablage kopiert", "image_copied_to_clipboard": "Bild-Daten-URI in die Zwischenablage kopiert", "image_saved": "Bild ges<PERSON> unter {{path}}", "organization_share_link_copied": "Organisations-Freigabelink in die Zwischenablage kopiert!", "public_share_link_copied": "Öffentlicher Freigabelink in die Zwischenablage kopiert!", "mode_exported": "Modus '{{mode}}' erfolgreich exportiert", "mode_imported": "Modus erfolgreich importiert"}, "answers": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>", "remove": "Entfernen", "keep": "Behalten"}, "buttons": {"save": "Speichern", "edit": "<PERSON><PERSON><PERSON>"}, "tasks": {"canceled": "Aufgabenfehler: Die Aufgabe wurde vom Benutzer gestoppt und abgebrochen.", "deleted": "Aufgabenfehler: Die Aufgabe wurde vom Benutzer gestoppt und gelöscht.", "incomplete": "Aufgabe #{{taskNumber}} (Unvollständig)", "no_messages": "Aufgabe #{{taskNumber}} (<PERSON><PERSON>)"}, "storage": {"prompt_custom_path": "Gib den benutzerdefinierten Speicherpfad für den Gesprächsverlauf ein, leer lassen für Standardspeicherort", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "Bitte gib einen absoluten Pfad ein (z.B. D:\\RooCodeStorage oder /home/<USER>/storage)", "enter_valid_path": "Bitte gib einen gültigen Pfad ein"}, "input": {"task_prompt": "Was soll Roo tun?", "task_placeholder": "Gib deine Aufgabe hier ein"}, "settings": {"providers": {"groqApiKey": "Groq API-Schlüssel", "getGroqApiKey": "Groq API-Schlüssel erhalten", "claudeCode": {"pathLabel": "Claude Code Pfad", "description": "Optionaler Pfad zu deiner Claude Code CLI. Standardmäßig 'claude', falls nicht festgelegt.", "placeholder": "Standard: claude"}}}, "customModes": {"errors": {"yamlParseError": "Ungültiges YAML in .roomodes-Datei in Zeile {{line}}. Bitte überprüfe:\n• Korrekte Einrückung (verwende Leerzeichen, keine <PERSON>bs)\n• Passende Anführungszeichen und Klammern\n• Gültige YAML-Syntax", "schemaValidationError": "Ungültiges Format für benutzerdefinierte Modi in .roomodes:\n{{issues}}", "invalidFormat": "Ungültiges Format für benutzerdefinierte Modi. <PERSON><PERSON> stelle sicher, dass deine Einstellungen dem korrekten YAML-Format folgen.", "updateFailed": "Fehler beim Aktualisieren des benutzerdefinierten Modus: {{error}}", "deleteFailed": "<PERSON><PERSON> beim Löschen des benutzerdefinierten Modus: {{error}}", "resetFailed": "<PERSON><PERSON> beim Zurücksetzen der benutzerdefinierten Modi: {{error}}", "modeNotFound": "Schreibfehler: Modus nicht gefunden", "noWorkspaceForProject": "<PERSON>in Arbeitsbereich-Ordner für projektspezifischen Modus gefunden"}, "scope": {"project": "projekt", "global": "global"}}, "mdm": {"errors": {"cloud_auth_required": "Deine Organisation erfordert eine Roo Code Cloud-Authentifizierung. Bitte melde dich an, um fortzufahren.", "organization_mismatch": "Du musst mit dem Roo Code Cloud-Konto deiner Organisation authentifiziert sein.", "verification_failed": "Die Organisationsauthentifizierung konnte nicht verifiziert werden."}}, "prompts": {"deleteMode": {"title": "Benutzerdefinierten Modus löschen", "description": "<PERSON><PERSON> du sicher, dass du diesen {{scope}}-Modus löschen möchtest? <PERSON><PERSON>ch wird auch der zugehörige Regelordner unter {{rulesFolderPath}} gelöscht", "descriptionNoRules": "B<PERSON> du sicher, dass du diesen benutzerdefinierten Modus löschen möchtest?", "confirm": "Löschen"}}, "commands": {"preventCompletionWithOpenTodos": {"description": "Aufgabenabschluss verhindern, wenn unvollständige Todos in der Todo-Liste vorhanden sind"}}}