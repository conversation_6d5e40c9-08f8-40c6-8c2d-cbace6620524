# Workspace Multiple Folder Support for CodeIndexManager

## Objective

Enhance the `CodeIndexManager` to support indexing multiple workspace folders independently, rather than just the first one. Additionally, ensure that the `code-index` panel's displayed indexing information updates dynamically when the active workspace folder changes.

## Current Limitations

Currently, the `CodeIndexManager` and its associated services are designed to operate on a single workspace path, specifically the first workspace folder returned by `vscode.workspace.workspaceFolders[0]`. This limits the code indexing feature to only one part of a multi-root workspace.

## Proposed Design

The core idea is to manage a separate `CodeIndexManager` instance for each active workspace folder. This allows each folder to maintain its own index, cache, and state. A new central component will be responsible for orchestrating these individual `CodeIndexManager` instances and responding to workspace folder changes and active editor changes.

### Key Components and Their Roles:

1.  **`CodeIndexManager` (Modified):**
    *   Will no longer be a strict singleton tied to `workspaceFolders[0]`. Instead, it will be instantiated per workspace folder.
    *   The `static instances` map will continue to store `CodeIndexManager` instances, keyed by their respective `workspacePath`.
    *   A new static method, `CodeIndexManager.getOrCreateInstance(workspacePath: string, context: vscode.ExtensionContext)`, will be introduced to retrieve or create an instance for a given path.
    *   Its `initialize()` and `startIndexing()` methods will operate on its specific `workspacePath`.

2.  **`WorkspaceIndexManager` (New Class):**
    *   This new class will be responsible for managing the lifecycle of `CodeIndexManager` instances for all active workspace folders.
    *   It will hold a `Map<string, CodeIndexManager>` to manage instances per workspace path.
    *   It will listen to `vscode.workspace.onDidChangeWorkspaceFolders` to detect additions or removals of folders.
    *   When a folder is added, it will call `CodeIndexManager.getOrCreateInstance()` and `initialize()` for that folder.
    *   When a folder is removed, it will call `dispose()` on the corresponding `CodeIndexManager` instance and remove it from its internal tracking.
    *   It will also listen to `vscode.window.onDidChangeActiveTextEditor` to determine the currently active workspace folder. This information will be used to update the UI.

3.  **`ClineProvider` (Modified):**
    *   Will be updated to hold an instance of the `WorkspaceIndexManager`.
    *   It will expose methods to retrieve the `CodeIndexManager` for the currently active workspace folder.
    *   It will send messages to the webview to update the `code-index` panel when the active workspace folder changes, or when the indexing status of the active folder changes.

4.  **`webviewMessageHandler` (Modified):**
    *   Will be updated to handle messages related to workspace folder changes.
    *   The `requestIndexingStatus` message will be enhanced to allow requesting status for a specific `workspacePath`. If no path is provided, it will default to the currently active workspace folder.
    *   The `startIndexing` and `clearIndexData` messages will also be updated to operate on the active workspace folder's index.

5.  **`CodeIndexSearchService` (Modified):**
    *   The `searchIndex` method will operate on the `vectorStore` associated with the `CodeIndexManager` of the *active* workspace folder. This ensures that search results are relevant to the user's current context.

### Data Flow and Interactions:

```mermaid
graph TD
    A[VS Code Extension Activation] --> B{Initialize WorkspaceIndexManager};
    B --> C[Listen to workspace folder changes];
    B --> D[Listen to active text editor changes];

    C -- Add/Remove Folder --> E{Update CodeIndexManager instances};
    E -- For each folder --> F[CodeIndexManager.getOrCreateInstance(folderPath)];
    F --> G[Initialize CodeIndexManager];

    D -- Active Folder Change --> H[ClineProvider];
    H -- Request status for active folder --> I[webviewMessageHandler];
    I -- Get CodeIndexManager for active folder --> J[CodeIndexManager.getCurrentStatus()];
    J --> K[Send indexingStatusUpdate to Webview];

    L[User Action in Webview] --> I;
    I -- startIndexing/clearIndexData --> J;
    J -- Call on active CodeIndexManager --> M[CodeIndexOrchestrator];
    M -- Perform indexing/clearing --> N[Update CodeIndexStateManager];
    N --> K;

    O[CodeIndexManager] -- Search Query --> P[CodeIndexSearchService];
    P -- Use active folder's VectorStore --> Q[QdrantVectorStore];
```

## Implementation Plan (Todo List)

This plan outlines the steps to implement multi-folder indexing support.

1.  **Refactor `CodeIndexManager`:**
    *   [ ] Modify `CodeIndexManager.getInstance` to accept an optional `workspacePath` and return the instance for that path. If no path is provided, it should default to the *first* workspace folder (for backward compatibility, but this will be superseded by `WorkspaceIndexManager`).
    *   [ ] Introduce a new static method `CodeIndexManager.getOrCreateInstance(workspacePath: string, context: vscode.ExtensionContext)` that ensures a `CodeIndexManager` instance exists for the given `workspacePath`.
    *   [ ] Update the constructor of `CodeIndexManager` to explicitly take `workspacePath` and `context`.
    *   [ ] Ensure `dispose()` method correctly cleans up resources for its specific `workspacePath`.

2.  **Create `WorkspaceIndexManager`:**
    *   [ ] Create a new class `src/services/code-index/workspace-index-manager.ts`.
    *   [ ] This class will hold a `Map<string, CodeIndexManager>` to manage instances per workspace path.
    *   [ ] Implement a constructor that takes `vscode.ExtensionContext` and `ContextProxy`.
    *   [ ] Implement an `initialize()` method that:
        *   Iterates through `vscode.workspace.workspaceFolders` and calls `CodeIndexManager.getOrCreateInstance()` for each.
        *   Subscribes to `vscode.workspace.onDidChangeWorkspaceFolders` to handle additions/removals.
        *   Subscribes to `vscode.window.onDidChangeActiveTextEditor` to track the active workspace folder.
    *   [ ] Implement methods to add/remove `CodeIndexManager` instances based on workspace folder changes.
    *   [ ] Add a method `getActiveCodeIndexManager()` that returns the `CodeIndexManager` for the currently active workspace folder.

3.  **Integrate `WorkspaceIndexManager` into `ClineProvider`:**
    *   [ ] In `src/core/webview/ClineProvider.ts`, add an instance of `WorkspaceIndexManager`.
    *   [ ] Initialize `WorkspaceIndexManager` during `ClineProvider`'s activation.
    *   [ ] Update `ClineProvider`'s `codeIndexManager` getter to return the active manager from `WorkspaceIndexManager`.

4.  **Update `webviewMessageHandler`:**
