{"unknownError": "不明なエラー", "authenticationFailed": "埋め込みの作成に失敗しました：認証に失敗しました。APIキーを確認してください。", "failedWithStatus": "{{attempts}}回試行しましたが、埋め込みの作成に失敗しました：HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "{{attempts}}回試行しましたが、埋め込みの作成に失敗しました：{{errorMessage}}", "failedMaxAttempts": "{{attempts}}回試行しましたが、埋め込みの作成に失敗しました", "textExceedsTokenLimit": "インデックス{{index}}のテキストが最大トークン制限を超えています（{{itemTokens}}> {{maxTokens}}）。スキップします。", "rateLimitRetry": "レート制限に達しました。{{delayMs}}ミリ秒後に再試行します（試行{{attempt}}/{{maxRetries}}）", "ollama": {"couldNotReadErrorBody": "エラー本文を読み取れませんでした", "requestFailed": "Ollama APIリクエストが失敗しました。ステータス {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "Ollama APIからの無効な応答構造：\"embeddings\"配列が見つからないか、配列ではありません。", "embeddingFailed": "<PERSON><PERSON><PERSON>埋め込みが失敗しました：{{message}}", "serviceNotRunning": "Ollamaサービスは{{baseUrl}}で実行されていません", "serviceUnavailable": "Ollamaサービスは利用できません（ステータス：{{status}}）", "modelNotFound": "Ollamaモデルが見つかりません：{{modelId}}", "modelNotEmbeddingCapable": "Ollamaモデルは埋め込みに対応していません：{{modelId}}", "hostNotFound": "Ollamaホストが見つかりません：{{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "ファイル{{filePath}}の処理中に不明なエラーが発生しました", "unknownErrorDeletingPoints": "{{filePath}}のポイント削除中に不明なエラーが発生しました", "failedToProcessBatchWithError": "{{maxRetries}}回の試行後、バッチ処理に失敗しました：{{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Qdrantベクターデータベースへの接続に失敗しました。Qdrantが実行中で{{qdrantUrl}}でアクセス可能であることを確認してください。エラー：{{errorMessage}}", "vectorDimensionMismatch": "新しいモデルのベクトルインデックスの更新に失敗しました。インデックスをクリアして再試行してください。詳細：{{errorMessage}}"}, "validation": {"authenticationFailed": "認証に失敗しました。設定でAPIキーを確認してください。", "connectionFailed": "エンベッダーサービスへの接続に失敗しました。接続設定を確認し、サービスが実行されていることを確認してください。", "modelNotAvailable": "指定されたモデルは利用できません。モデル構成を確認してください。", "configurationError": "無効なエンベッダー構成です。設定を確認してください。", "serviceUnavailable": "エンベッダーサービスは利用できません。実行中でアクセス可能であることを確認してください。", "invalidEndpoint": "無効なAPIエンドポイントです。URL構成を確認してください。", "invalidEmbedderConfig": "無効なエンベッダー構成です。設定を確認してください。", "invalidApiKey": "無効なAPIキーです。APIキー構成を確認してください。", "invalidBaseUrl": "無効なベースURLです。URL構成を確認してください。", "invalidModel": "無効なモデルです。モデル構成を確認してください。", "invalidResponse": "エンベッダーサービスからの無効な応答です。設定を確認してください。", "apiKeyRequired": "このエンベッダーにはAPIキーが必要です。", "baseUrlRequired": "このエンベッダーにはベースURLが必要です"}, "serviceFactory": {"openAiConfigMissing": "エンベッダー作成のためのOpenAI設定がありません", "ollamaConfigMissing": "エンベッダー作成のためのOllama設定がありません", "openAiCompatibleConfigMissing": "エンベッダー作成のためのOpenAI互換設定がありません", "geminiConfigMissing": "エンベッダー作成のためのGemini設定がありません", "mistralConfigMissing": "エンベッダー作成のためのMistral設定がありません", "invalidEmbedderType": "無効なエンベッダータイプが設定されています: {{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "プロバイダー '{{provider}}' のモデル '{{modelId}}' の埋め込み次元を決定できませんでした。OpenAI互換プロバイダー設定で「埋め込み次元」が正しく設定されていることを確認してください。", "vectorDimensionNotDetermined": "プロバイダー '{{provider}}' のモデル '{{modelId}}' の埋め込み次元を決定できませんでした。モデルプロファイルまたは設定を確認してください。", "qdrantUrlMissing": "ベクターストア作成のためのQdrant URLがありません", "codeIndexingNotConfigured": "サービスを作成できません: コードインデックスが正しく設定されていません"}}