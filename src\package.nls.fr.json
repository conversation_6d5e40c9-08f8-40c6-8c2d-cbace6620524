{"extension.displayName": "Roo Code (anciennement Roo Cline)", "extension.description": "Une équipe complète de développement d'agents IA dans votre éditeur.", "command.newTask.title": "Nouvelle Tâche", "command.explainCode.title": "Expliquer le Code", "command.fixCode.title": "Corriger le Code", "command.improveCode.title": "Améliorer le Code", "command.addToContext.title": "A<PERSON>ter au Contexte", "command.openInNewTab.title": "Ouv<PERSON>r dans un Nouvel Onglet", "command.focusInput.title": "Focus sur le Champ de Saisie", "command.setCustomStoragePath.title": "Définir le Chemin de Stockage Personnalisé", "command.importSettings.title": "Importer les Paramètres", "command.terminal.addToContext.title": "Ajouter le Contenu du Terminal au Contexte", "command.terminal.fixCommand.title": "<PERSON>rri<PERSON> cette <PERSON>e", "command.terminal.explainCommand.title": "Expliquer cette <PERSON>e", "command.acceptInput.title": "Accepter l'Entrée/Suggestion", "views.activitybar.title": "Roo Code", "views.contextMenu.label": "Roo Code", "views.terminalMenu.label": "Roo Code", "views.sidebar.name": "Roo Code", "command.mcpServers.title": "Serveurs MCP", "command.prompts.title": "Modes", "command.history.title": "Historique", "command.marketplace.title": "<PERSON><PERSON>", "command.openInEditor.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> dans l'Éditeur", "command.settings.title": "Paramètres", "command.documentation.title": "Documentation", "configuration.title": "Roo Code", "commands.allowedCommands.description": "Commandes pouvant être exécutées automatiquement lorsque 'Toujours approuver les opérations d'exécution' est activé", "commands.deniedCommands.description": "Préfixes de commandes qui seront automatiquement refusés sans demander d'approbation. En cas de conflit avec les commandes autorisées, la correspondance de préfixe la plus longue a la priorité. Ajouter * pour refuser toutes les commandes.", "commands.commandExecutionTimeout.description": "Temps maximum en secondes pour attendre que l'exécution de la commande se termine avant expiration (0 = pas de délai, 1-600s, défaut : 0s)", "commands.commandTimeoutAllowlist.description": "Préfixes de commandes qui sont exclus du délai d'exécution des commandes. Les commandes correspondant à ces préfixes s'exécuteront sans restrictions de délai.", "settings.vsCodeLmModelSelector.description": "Paramètres pour l'API du modèle de langage VSCode", "settings.vsCodeLmModelSelector.vendor.description": "Le fournisseur du modèle de langage (ex: copilot)", "settings.vsCodeLmModelSelector.family.description": "La famille du modèle de langage (ex: gpt-4)", "settings.customStoragePath.description": "Chemin de stockage personnalisé. Laisser vide pour utiliser l'emplacement par défaut. Prend en charge les chemins absolus (ex: 'D:\\RooCodeStorage')", "settings.enableCodeActions.description": "Activer les correctifs rapides de Roo Code.", "settings.autoImportSettingsPath.description": "Chemin d'accès à un fichier de configuration RooCode à importer automatiquement au démarrage de l'extension. Prend en charge les chemins absolus et les chemins relatifs au répertoire de base (par exemple, '~/Documents/roo-code-settings.json'). Laisser vide pour désactiver l'importation automatique."}