{"common": {"save": "Сохранить", "done": "Готово", "cancel": "Отмена", "reset": "Сбросить", "select": "Выбрать", "add": "Добавить заголовок", "remove": "Удалить"}, "header": {"title": "Настройки", "saveButtonTooltip": "Сохранить изменения", "nothingChangedTooltip": "Изменений нет", "doneButtonTooltip": "Отменить несохранённые изменения и закрыть панель настроек"}, "unsavedChangesDialog": {"title": "Несохранённые изменения", "description": "Вы хотите отменить изменения и продолжить?", "cancelButton": "Отмена", "discardButton": "Отменить изменения"}, "sections": {"providers": "Провайдеры", "autoApprove": "Автоодобрение", "browser": "Доступ к компьютеру", "checkpoints": "Контрольные точки", "notifications": "Уведомления", "contextManagement": "Контекст", "terminal": "<PERSON>ер<PERSON><PERSON><PERSON><PERSON>", "prompts": "Промпты", "experimental": "Экспериментальное", "language": "Язык", "about": "О Roo Code"}, "prompts": {"description": "Настройте промпты поддержки, используемые для быстрых действий, таких как улучшение промптов, объяснение кода и исправление проблем. Эти промпты помогают Roo обеспечить лучшую поддержку для общих задач разработки."}, "codeIndex": {"title": "Индексация кодовой базы", "enableLabel": "Включить индексацию кодовой базы", "enableDescription": "Включите индексацию кода для улучшения поиска и понимания контекста", "providerLabel": "Провайдер эмбеддингов", "selectProviderPlaceholder": "Выберите провайдера", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "geminiProvider": "Gemini", "geminiApiKeyLabel": "Ключ API:", "geminiApiKeyPlaceholder": "Введите свой API-ключ Gemini", "mistralProvider": "<PERSON><PERSON><PERSON>", "mistralApiKeyLabel": "Ключ API:", "mistralApiKeyPlaceholder": "Введите свой API-ключ Mistral", "openaiCompatibleProvider": "OpenAI-совместимый", "openAiKeyLabel": "Ключ API OpenAI", "openAiKeyPlaceholder": "Введите ваш ключ API OpenAI", "openAiCompatibleBaseUrlLabel": "Базовый URL", "openAiCompatibleApiKeyLabel": "Ключ API", "openAiCompatibleApiKeyPlaceholder": "Введите ваш ключ API", "openAiCompatibleModelDimensionLabel": "Размерность эмбеддинга:", "modelDimensionLabel": "Размерность модели", "openAiCompatibleModelDimensionPlaceholder": "напр., 1536", "openAiCompatibleModelDimensionDescription": "Размерность эмбеддинга (размер выходных данных) для вашей модели. Проверьте документацию вашего провайдера для этого значения. Распространенные значения: 384, 768, 1536, 3072.", "modelLabel": "Модель", "selectModelPlaceholder": "Выберите модель", "ollamaUrlLabel": "URL Ollama:", "qdrantUrlLabel": "URL Qdrant", "qdrantKeyLabel": "<PERSON><PERSON><PERSON><PERSON> Qdrant:", "startIndexingButton": "Начать", "clearIndexDataButton": "Очистить индекс", "unsavedSettingsMessage": "Пожалуйста, сохрани настройки перед запуском процесса индексации.", "clearDataDialog": {"title": "Вы уверены?", "description": "Это действие нельзя отменить. Оно навсегда удалит данные индекса вашей кодовой базы.", "cancelButton": "Отмена", "confirmButton": "Очистить данные"}, "description": "Настройте параметры индексации кодовой базы для включения семантического поиска в вашем проекте. <0>Узнать больше</0>", "statusTitle": "Статус", "settingsTitle": "Настройки индексации", "disabledMessage": "Индексация кодовой базы в настоящее время отключена. Включите ее в глобальных настройках для настройки параметров индексации.", "embedderProviderLabel": "Провайдер эмбеддера", "modelPlaceholder": "Введите название модели", "selectModel": "Выберите модель", "ollamaBaseUrlLabel": "Базовый URL Ollama", "qdrantApiKeyLabel": "API-к<PERSON><PERSON><PERSON> Qdrant", "qdrantApiKeyPlaceholder": "Введите ваш API-к<PERSON>ю<PERSON> Qdrant (необязательно)", "setupConfigLabel": "Настройка", "ollamaUrlPlaceholder": "http://localhost:11434", "openAiCompatibleBaseUrlPlaceholder": "https://api.example.com", "modelDimensionPlaceholder": "1536", "qdrantUrlPlaceholder": "http://localhost:6333", "saveError": "Не удалось сохранить настройки", "modelDimensions": "({{dimension}} измерений)", "saveSuccess": "Настройки успешно сохранены", "saving": "Сохранение...", "saveSettings": "Сохранить", "indexingStatuses": {"standby": "Ожидание", "indexing": "Индексация", "indexed": "Проиндексировано", "error": "Ошибка"}, "close": "Закрыть", "validation": {"invalidQdrantUrl": "Неверный URL Qdrant", "invalidOllamaUrl": "Неверный URL Ollama", "invalidBaseUrl": "Неверный базовый URL", "qdrantUrlRequired": "Требуется URL Qdrant", "openaiApiKeyRequired": "Требуется ключ API OpenAI", "modelSelectionRequired": "Требуется выбор модели", "apiKeyRequired": "Требуется ключ API", "modelIdRequired": "Требуется идентификатор модели", "modelDimensionRequired": "Требуется размерность модели", "geminiApiKeyRequired": "Требуется ключ API Gemini", "mistralApiKeyRequired": "Требуется API-ключ Mistral", "ollamaBaseUrlRequired": "Требуется базовый URL Ollama", "baseUrlRequired": "Требуется базовый URL", "modelDimensionMinValue": "Размерность модели должна быть больше 0"}, "advancedConfigLabel": "Расширенная конфигурация", "searchMinScoreLabel": "Порог оценки поиска", "searchMinScoreDescription": "Минимальный балл сходства (0.0-1.0), необходимый для результатов поиска. Более низкие значения возвращают больше результатов, но они могут быть менее релевантными. Более высокие значения возвращают меньше результатов, но более релевантных.", "searchMinScoreResetTooltip": "Сбросить к значению по умолчанию (0.4)", "searchMaxResultsLabel": "Максимальное количество результатов поиска", "searchMaxResultsDescription": "Максимальное количество результатов поиска, возвращаемых при запросе индекса кодовой базы. Более высокие значения предоставляют больше контекста, но могут включать менее релевантные результаты.", "resetToDefault": "Сбросить к значению по умолчанию"}, "autoApprove": {"description": "Разрешить Roo автоматически выполнять операции без необходимости одобрения. Включайте эти параметры только если полностью доверяете ИИ и понимаете связанные с этим риски безопасности.", "toggleAriaLabel": "Переключить автоодобрение", "disabledAriaLabel": "Автоодобрение отключено - сначала выберите опции", "readOnly": {"label": "Чтение", "description": "Если включено, Roo будет автоматически просматривать содержимое каталогов и читать файлы без необходимости нажимать кнопку \"Одобрить\".", "outsideWorkspace": {"label": "Включая файлы вне рабочей области", "description": "Разрешить Roo читать файлы вне текущей рабочей области без необходимости одобрения."}}, "write": {"label": "Запись", "description": "Автоматически создавать и редактировать файлы без необходимости одобрения", "delayLabel": "Задержка после записи для диагностики возможных проблем", "outsideWorkspace": {"label": "Включая файлы вне рабочей области", "description": "Разрешить Roo создавать и редактировать файлы вне текущей рабочей области без необходимости одобрения."}, "protected": {"label": "Включить защищенные файлы", "description": "Разрешить Roo создавать и редактировать защищенные файлы (такие как .rooignore и файлы конфигурации .roo/) без необходимости одобрения."}}, "browser": {"label": "Браузер", "description": "Автоматически выполнять действия в браузере без необходимости одобрения. Применяется только, если модель поддерживает использование компьютера"}, "retry": {"label": "Повтор", "description": "Автоматически повторять неудачные запросы к API при ошибке сервера", "delayLabel": "Задержка перед повтором запроса"}, "mcp": {"label": "MCP", "description": "Включить автоодобрение отдельных инструментов MCP в представлении MCP Servers (требуется включить как этот параметр, так и индивидуальный чекбокс инструмента \"Всегда разрешать\")"}, "modeSwitch": {"label": "Режим", "description": "Автоматически переключаться между разными режимами без необходимости одобрения"}, "subtasks": {"label": "Подзадачи", "description": "Разрешить создание и выполнение подзадач без необходимости одобрения"}, "followupQuestions": {"label": "Вопрос", "description": "Автоматически выбирать первый предложенный ответ на дополнительные вопросы после настроенного тайм-аута", "timeoutLabel": "Время ожидания перед автоматическим выбором первого ответа"}, "execute": {"label": "Выполнение", "description": "Автоматически выполнять разрешённые команды терминала без необходимости одобрения", "allowedCommands": "Разрешённые авто-выполняемые команды", "allowedCommandsDescription": "Префиксы команд, которые могут быть автоматически выполнены при включённом параметре \"Всегда одобрять выполнение операций\". Добавьте * для разрешения всех команд (используйте с осторожностью).", "deniedCommands": "Запрещенные команды", "deniedCommandsDescription": "Префиксы команд, которые будут автоматически отклонены без запроса одобрения. В случае конфликтов с разрешенными командами, приоритет имеет самое длинное совпадение префикса. Добавьте * чтобы запретить все команды.", "commandPlaceholder": "Введите префикс команды (например, 'git ')", "deniedCommandPlaceholder": "Введите префикс команды для запрета (например, 'rm -rf')", "addButton": "Добавить", "autoDenied": "Команды с префиксом `{{prefix}}` были запрещены пользователем. Не обходи это ограничение, выполняя другую команду."}, "updateTodoList": {"label": "Todo", "description": "Список дел обновляется автоматически без подтверждения"}, "apiRequestLimit": {"title": "Максимум запросов", "description": "Автоматически выполнять это количество API-запросов перед запросом разрешения на продолжение задачи.", "unlimited": "Без ограничений"}, "selectOptionsFirst": "Выберите хотя бы один вариант ниже, чтобы включить автоодобрение"}, "providers": {"providerDocumentation": "Документация {{provider}}", "configProfile": "Профиль конфигурации", "description": "Сохраняйте различные конфигурации API для быстрого переключения между провайдерами и настройками.", "apiProvider": "Провайдер API", "model": "Модель", "nameEmpty": "Имя не может быть пустым", "nameExists": "Профиль с таким именем уже существует", "deleteProfile": "Удалить профиль", "invalidArnFormat": "Неверный формат ARN. Пожалуйста, проверьте примеры выше.", "enterNewName": "Введите новое имя", "addProfile": "Добавить профиль", "renameProfile": "Переименовать профиль", "newProfile": "Новый профиль конфигурации", "enterProfileName": "Введите имя профиля", "createProfile": "Создать профиль", "cannotDeleteOnlyProfile": "Нельзя удалить единственный профиль", "searchPlaceholder": "Поиск профилей", "searchProviderPlaceholder": "Поиск провайдеров", "noProviderMatchFound": "Провайдеры не найдены", "noMatchFound": "Совпадений не найдено", "vscodeLmDescription": "API языковой модели VS Code позволяет запускать модели, предоставляемые другими расширениями VS Code (включая, но не ограничиваясь GitHub Copilot). Для начала установите расширения Copilot и Copilot Chat из VS Code Marketplace.", "awsCustomArnUse": "Введите действительный Amazon Bedrock ARN для используемой модели. Примеры формата:", "awsCustomArnDesc": "Убедитесь, что регион в ARN совпадает с выбранным выше регионом AWS.", "openRouterApiKey": "OpenRouter API-кл<PERSON>ч", "getOpenRouterApiKey": "Получить OpenRouter API-ключ", "apiKeyStorageNotice": "API-ключи хранятся безопасно в Secret Storage VSCode", "glamaApiKey": "Glama API-ключ", "getGlamaApiKey": "Получить Glama API-ключ", "useCustomBaseUrl": "Использовать пользовательский базовый URL", "useReasoning": "Включить рассуждения", "useHostHeader": "Использовать пользовательский Host-заголовок", "useLegacyFormat": "Использовать устаревший формат OpenAI API", "customHeaders": "Пользовательские заголовки", "headerName": "Имя заголовка", "headerValue": "Значение заголовка", "noCustomHeaders": "Пользовательские заголовки не определены. Нажмите кнопку +, чтобы добавить.", "requestyApiKey": "Requesty API-ключ", "refreshModels": {"label": "Обновить модели", "hint": "Пожалуйста, откройте настройки заново, чтобы увидеть последние модели.", "loading": "Обновление списка моделей...", "success": "Список моделей успешно обновлен!", "error": "Не удалось обновить список моделей. Пожалуйста, попробуйте снова."}, "getRequestyApiKey": "Получить Requesty API-ключ", "openRouterTransformsText": "Сжимать подсказки и цепочки сообщений до размера контекста (<a>OpenRouter Transforms</a>)", "anthropicApiKey": "Anthropic API-ключ", "getAnthropicApiKey": "Получить Anthropic API-ключ", "anthropicUseAuthToken": "Передавать Anthropic API-ключ как Authorization-заголовок вместо X-Api-Key", "chutesApiKey": "Chutes API-ключ", "getChutesApiKey": "Получить Chutes API-ключ", "deepSeekApiKey": "DeepSeek API-кл<PERSON>ч", "getDeepSeekApiKey": "Получить DeepSeek API-ключ", "moonshotApiKey": "Moonshot API-к<PERSON><PERSON>ч", "getMoonshotApiKey": "Получить Moonshot API-ключ", "moonshotBaseUrl": "Точка входа Moonshot", "geminiApiKey": "Gemini API-кл<PERSON>ч", "getGroqApiKey": "Получить Groq API-ключ", "groqApiKey": "Groq API-ключ", "getGeminiApiKey": "Получить Gemini API-ключ", "apiKey": "API-к<PERSON><PERSON><PERSON>", "openAiApiKey": "OpenAI API-ключ", "openAiBaseUrl": "Базовый URL", "getOpenAiApiKey": "Получить OpenAI API-ключ", "mistralApiKey": "Mistral API-ключ", "getMistralApiKey": "Получить Mistral / Codestral API-ключ", "codestralBaseUrl": "Базовый URL Codestral (опционально)", "codestralBaseUrlDesc": "Укажите альтернативный URL для модели Codestral.", "xaiApiKey": "xAI API-ключ", "getXaiApiKey": "Получить xAI API-ключ", "litellmApiKey": "API-ключ LiteLLM", "litellmBaseUrl": "Базовый URL LiteLLM", "awsCredentials": "AWS-учётные данные", "awsProfile": "Профиль AWS", "awsProfileName": "Имя профиля AWS", "awsAccessKey": "AWS Access Key", "awsSecretKey": "AWS Secret Key", "awsSessionToken": "AWS Session Token", "awsRegion": "Регион AWS", "awsCrossRegion": "Использовать кросс-региональный вывод", "awsBedrockVpc": {"useCustomVpcEndpoint": "Использовать пользовательскую конечную точку VPC", "vpcEndpointUrlPlaceholder": "Введите URL конечной точки VPC (опционально)", "examples": "Примеры:"}, "enablePromptCaching": "Включить кэширование подсказок", "enablePromptCachingTitle": "Включить кэширование подсказок для повышения производительности и снижения затрат для поддерживаемых моделей.", "cacheUsageNote": "Примечание: если вы не видите использование кэша, попробуйте выбрать другую модель, а затем вернуться к нужной.", "vscodeLmModel": "Языковая модель", "vscodeLmWarning": "Внимание: это очень экспериментальная интеграция, поддержка провайдера может отличаться. Если возникает ошибка о неподдерживаемой модели — проблема на стороне провайдера.", "googleCloudSetup": {"title": "Для использования Google Cloud Vertex AI необходимо:", "step1": "1. Создайте аккаунт Google Cloud, включите Vertex AI API и нужные модели Claude.", "step2": "2. Установите Google Cloud CLI и настройте учетные данные по умолчанию.", "step3": "3. Или создайте сервисный аккаунт с ключом."}, "googleCloudCredentials": "Учётные данные Google Cloud", "googleCloudKeyFile": "Путь к ключу Google Cloud", "googleCloudProjectId": "ID проекта Google Cloud", "googleCloudRegion": "Регион Google Cloud", "lmStudio": {"baseUrl": "Базовый URL (опционально)", "modelId": "ID модели", "speculativeDecoding": "Включить speculative decoding", "draftModelId": "ID черновой модели", "draftModelDesc": "Черновая модель должна быть из той же семьи моделей для корректной работы speculative decoding.", "selectDraftModel": "Выбрать черновую модель", "noModelsFound": "Черновых моделей не найдено. Проверьте, что LM Studio запущен с включённым серверным режимом.", "description": "LM Studio позволяет запускать модели локально на вашем компьютере. Для начала ознакомьтесь с <a>кратким руководством</a>. Также необходимо включить <b>локальный сервер</b> LM Studio для работы с этим расширением. <span>Примечание:</span> Roo Code использует сложные подсказки и лучше всего работает с моделями Claude. Менее мощные модели могут работать некорректно."}, "ollama": {"baseUrl": "Базовый URL (опционально)", "modelId": "ID модели", "description": "Ollama позволяет запускать модели локально на вашем компьютере. Для начала ознакомьтесь с кратким руководством.", "warning": "Примечание: Roo Code использует сложные подсказки и лучше всего работает с моделями Claude. Менее мощные модели могут работать некорректно."}, "unboundApiKey": "Unbound API-ключ", "getUnboundApiKey": "Получить Unbound API-ключ", "unboundRefreshModelsSuccess": "Список моделей обновлен! Теперь вы можете выбрать из последних моделей.", "unboundInvalidApiKey": "Недействительный API-ключ. Пожалуйста, проверьте ваш API-ключ и попробуйте снова.", "humanRelay": {"description": "API-ключ не требуется, но пользователю нужно вручную копировать и вставлять информацию в веб-чат ИИ.", "instructions": "Во время использования появится диалоговое окно, и текущее сообщение будет скопировано в буфер обмена автоматически. Вам нужно вставить его в веб-версию ИИ (например, ChatGPT или Claude), затем скопировать ответ ИИ обратно в диалоговое окно и нажать кнопку подтверждения."}, "openRouter": {"providerRouting": {"title": "Маршрутизация провайдера OpenRouter", "description": "OpenRouter направляет запросы к лучшим доступным провайдерам для вашей модели. По умолчанию запросы балансируются между топовыми провайдерами для максимальной доступности. Однако вы можете выбрать конкретного провайдера для этой модели.", "learnMore": "Подробнее о маршрутизации провайдеров"}}, "customModel": {"capabilities": "Настройте возможности и стоимость вашей пользовательской модели, совместимой с OpenAI. Будьте осторожны при указании возможностей модели, это может повлиять на работу Roo Code.", "maxTokens": {"label": "Максимум токенов на вывод", "description": "Максимальное количество токенов, которые модель может сгенерировать в ответе. (Укажите -1, чтобы сервер сам определил максимум.)"}, "contextWindow": {"label": "Размер окна контекста", "description": "Общее количество токенов (вход + выход), которые модель может обработать."}, "imageSupport": {"label": "Поддержка изображений", "description": "Может ли эта модель обрабатывать и понимать изображения?"}, "computerUse": {"label": "Использование компьютера", "description": "Может ли эта модель взаимодействовать с браузером? (наприм<PERSON><PERSON>, Claude 3.7 Sonnet)."}, "promptCache": {"label": "Кэширование подсказок", "description": "Может ли эта модель кэшировать подсказки?"}, "pricing": {"input": {"label": "Цена за вход", "description": "Стоимость за миллион токенов во входном сообщении/подсказке. Влияет на стоимость отправки контекста и инструкций модели."}, "output": {"label": "Цена за вывод", "description": "Стоимость за миллион токенов в ответе модели. Влияет на стоимость генерируемого контента."}, "cacheReads": {"label": "Цена чтения из кэша", "description": "Стоимость за миллион токенов при чтении из кэша. Взимается при получении кэшированного ответа."}, "cacheWrites": {"label": "Цена записи в кэш", "description": "Стоимость за миллион токенов при записи в кэш. Взимается при первом кэшировании подсказки."}}, "resetDefaults": "Сбросить к значениям по умолчанию"}, "rateLimitSeconds": {"label": "Лимит скорости", "description": "Минимальное время между запросами к API."}, "consecutiveMistakeLimit": {"label": "Лимит ошибок и повторений", "description": "Количество последовательных ошибок или повторных действий перед показом диалогового окна 'У Roo возникли проблемы'", "unlimitedDescription": "Включены неограниченные повторные попытки (автоматическое продолжение). Диалоговое окно никогда не появится.", "warning": "⚠️ Установка значения 0 разрешает неограниченные повторные попытки, что может значительно увеличить использование API"}, "reasoningEffort": {"label": "Усилия по рассуждению модели", "high": "Высокие", "medium": "Средние", "low": "Низкие"}, "setReasoningLevel": "Включить усилие рассуждения", "claudeCode": {"pathLabel": "Путь к Claude Code", "description": "Необязательный путь к вашему Claude Code CLI. По умолчанию используется 'claude', если не установлено.", "placeholder": "По умолчанию: claude", "maxTokensLabel": "Макс. выходных токенов", "maxTokensDescription": "Максимальное количество выходных токенов для ответов Claude Code. По умолчанию 8000."}}, "browser": {"enable": {"label": "Включить инструмент браузера", "description": "Если включено, Roo может использовать браузер для взаимодействия с сайтами при использовании моделей, поддерживающих работу с компьютером. <0>Подробнее</0>"}, "viewport": {"label": "Размер окна просмотра", "description": "Выберите размер окна для взаимодействия с браузером. Влияет на отображение и взаимодействие с сайтами.", "options": {"largeDesktop": "Большой рабочий стол (1280x800)", "smallDesktop": "Маленький рабочий стол (900x600)", "tablet": "Планшет (768x1024)", "mobile": "Мобильный (360x640)"}}, "screenshotQuality": {"label": "Качество скриншота", "description": "Настройте качество WebP для скриншотов браузера. Более высокие значения дают более чёткие изображения, но увеличивают расход токенов."}, "remote": {"label": "Использовать удалённое подключение к браузеру", "description": "Подключиться к Chrome с включённым удалённым дебагом (--remote-debugging-port=9222).", "urlPlaceholder": "Пользовательский URL (например, http://localhost:9222)", "testButton": "Проверить соединение", "testingButton": "Проверка...", "instructions": "Введите адрес DevTools Protocol или оставьте поле пустым для автоматического поиска локальных экземпляров Chrome. Кнопка проверки попробует пользовательский URL, если он указан, или выполнит автопоиск."}}, "checkpoints": {"enable": {"label": "Включить автоматические контрольные точки", "description": "Если включено, Roo будет автоматически создавать контрольные точки во время выполнения задач, что упрощает просмотр изменений или возврат к предыдущим состояниям. <0>Подробнее</0>"}}, "notifications": {"sound": {"label": "Включить звуковые эффекты", "description": "Если включено, Roo будет воспроизводить звуковые эффекты для уведомлений и событий.", "volumeLabel": "Громкость"}, "tts": {"label": "Включить озвучивание", "description": "Если включено, Roo будет озвучивать свои ответы с помощью преобразования текста в речь.", "speedLabel": "Скорость"}}, "contextManagement": {"description": "Управляйте, какая информация включается в окно контекста ИИ, что влияет на расход токенов и качество ответов", "autoCondenseContextPercent": {"label": "Порог для запуска интеллектуального сжатия контекста", "description": "Когда контекстное окно достигает этого порога, Roo автоматически его сожмёт."}, "condensingApiConfiguration": {"label": "Конфигурация API для сжатия контекста", "description": "Выберите конфигурацию API для операций сжатия контекста. Оставьте невыбранным, чтобы использовать текущую активную конфигурацию.", "useCurrentConfig": "По умолчанию"}, "customCondensingPrompt": {"label": "Пользовательская подсказка для сжатия контекста", "description": "Пользовательская системная подсказка для сжатия контекста. Оставьте пустым, чтобы использовать подсказку по умолчанию.", "placeholder": "Введите здесь свой пользовательский промпт для сжатия...\n\nВы можете использовать ту же структуру, что и в промпте по умолчанию:\n- Предыдущий разговор\n- Текущая работа\n- Ключевые технические концепции\n- Соответствующие файлы и код\n- Решение проблем\n- Ожидающие задачи и следующие шаги", "reset": "Сбросить на значение по умолчанию", "hint": "Пусто = использовать промпт по умолчанию"}, "autoCondenseContext": {"name": "Автоматически запускать интеллектуальное сжатие контекста", "description": "Когда включено, Roo будет автоматически сжимать контекст при достижении порога. Когда отключено, вы все еще можете вручную запускать сжатие контекста."}, "openTabs": {"label": "Лимит контекста открытых вкладок", "description": "Максимальное количество открытых вкладок VSCode, включаемых в контекст. Большее значение даёт больше контекста, но увеличивает расход токенов."}, "workspaceFiles": {"label": "<PERSON>и<PERSON><PERSON>т контекста файлов рабочей области", "description": "Максимальное количество файлов, включаемых в детали текущей рабочей директории. Большее значение даёт больше контекста, но увеличивает расход токенов."}, "rooignore": {"label": "Показывать .rooignore-файлы в списках и поиске", "description": "Если включено, файлы, совпадающие с шаблонами в .rooignore, будут отображаться в списках с символом замка. Если выключено, такие файлы полностью скрываются из списков и поиска."}, "maxReadFile": {"label": "Порог автообрезки при чтении файла", "description": "Roo читает столько строк, если модель не указала явно начало/конец. Если число меньше общего количества строк в файле, Roo создаёт индекс определений кода по строкам. Особые случаи: -1 — Roo читает весь файл (без индексации), 0 — не читает строки, а создаёт только минимальный индекс. Меньшие значения минимизируют начальный контекст, позволяя точнее читать нужные диапазоны строк. Явные запросы начала/конца не ограничиваются этим параметром.", "lines": "строк", "always_full_read": "Всегда читать весь файл"}, "maxConcurrentFileReads": {"label": "Лимит одновременного чтения", "description": "Максимальное количество файлов, которые инструмент 'read_file' может обрабатывать одновременно. Более высокие значения могут ускорить чтение нескольких небольших файлов, но увеличивают использование памяти."}, "condensingThreshold": {"label": "Порог запуска сжатия", "selectProfile": "Настроить порог для профиля", "defaultProfile": "Глобальный по умолчанию (все профили)", "defaultDescription": "Когда контекст достигнет этого процента, он будет автоматически сжат для всех профилей, если у них нет пользовательских настроек", "profileDescription": "Пользовательский порог только для этого профиля (переопределяет глобальный по умолчанию)", "inheritDescription": "Этот профиль наследует глобальный порог по умолчанию ({{threshold}}%)", "usesGlobal": "(использует глобальный {{threshold}}%)"}}, "terminal": {"basic": {"label": "Настройки терминала: Основные", "description": "Основные настройки терминала"}, "advanced": {"label": "Настройки терминала: Расширенные", "description": "Следующие параметры могут потребовать перезапуск терминала для применения настроек."}, "outputLineLimit": {"label": "Лимит вывода терминала", "description": "Максимальное количество строк, включаемых в вывод терминала при выполнении команд. При превышении строки из середины будут удаляться для экономии токенов. <0>Подробнее</0>"}, "outputCharacterLimit": {"label": "Лимит символов терминала", "description": "Максимальное количество символов для включения в вывод терминала при выполнении команд. Этот лимит имеет приоритет над лимитом строк, чтобы предотвратить проблемы с памятью из-за чрезвычайно длинных строк. При превышении лимита вывод будет усечен. <0>Узнать больше</0>"}, "shellIntegrationTimeout": {"label": "Таймаут интеграции оболочки терминала", "description": "Максимальное время ожидания инициализации интеграции оболочки перед выполнением команд. Для пользователей с долгим стартом shell это значение можно увеличить, если появляются ошибки \"Shell Integration Unavailable\". <0>Подробнее</0>"}, "shellIntegrationDisabled": {"label": "Отключить интеграцию оболочки терминала", "description": "Включите это, если команды терминала не работают должным образом или вы видите ошибки 'Shell Integration Unavailable'. Это использует более простой метод выполнения команд, обходя некоторые расширенные функции терминала. <0>Подробнее</0>"}, "commandDelay": {"label": "Задержка команды терминала", "description": "Задержка в миллисекундах после выполнения команды. Значение по умолчанию 0 полностью отключает задержку. Это может помочь захватить весь вывод в терминалах с проблемами синхронизации. Обычно реализуется установкой `PROMPT_COMMAND='sleep N'`, в Powershell добавляется `start-sleep` в конец команды. Изначально было обходом бага VSCode #237208 и может не требоваться. <0>Подробнее</0>"}, "compressProgressBar": {"label": "Сжимать вывод прогресс-бара", "description": "Если включено, обрабатывает вывод терминала с возвратами каретки (\\r), имитируя отображение в реальном терминале. Промежуточные состояния прогресс-бара удаляются, остаётся только финальное, что экономит место в контексте. <0>Подробнее</0>"}, "powershellCounter": {"label": "Включить обходчик счётчика PowerShell", "description": "Если включено, добавляет счётчик к командам PowerShell для корректного выполнения. Помогает при проблемах с захватом вывода в терминалах PowerShell. <0>Подробнее</0>"}, "zshClearEolMark": {"label": "Очищать метку конца строки ZSH", "description": "Если включено, очищает PROMPT_EOL_MARK в zsh, чтобы избежать проблем с интерпретацией вывода, когда он заканчивается специальными символами типа '%'. <0>Подробнее</0>"}, "zshOhMy": {"label": "Включить интеграцию Oh My Zsh", "description": "Если включено, устанавливает ITERM_SHELL_INTEGRATION_INSTALLED=Yes для поддержки функций интеграции Oh My Zsh. Применение этой настройки может потребовать перезапуска IDE. <0>Подробнее</0>"}, "zshP10k": {"label": "Включить интеграцию Powerlevel10k", "description": "Если включено, устанавливает POWERLEVEL9K_TERM_SHELL_INTEGRATION=true для поддержки функций Powerlevel10k. <0>Подробнее</0>"}, "zdotdir": {"label": "Включить обработку ZDOTDIR", "description": "Если включено, создаёт временную директорию для ZDOTDIR для корректной интеграции zsh. Это обеспечивает корректную работу интеграции VSCode с zsh, сохраняя вашу конфигурацию. <0>Подробнее</0>"}, "inheritEnv": {"label": "Наследовать переменные среды", "description": "Если включено, терминал будет наследовать переменные среды от родительского процесса VSCode, такие как настройки интеграции оболочки, определённые в профиле пользователя. Напрямую переключает глобальную настройку VSCode `terminal.integrated.inheritEnv`. <0>Подробнее</0>"}}, "advancedSettings": {"title": "Дополнительные настройки"}, "advanced": {"diff": {"label": "Включить редактирование через диффы", "description": "Если включено, Roo сможет быстрее редактировать файлы и автоматически отклонять усечённые полные записи. Лучше всего работает с последней моделью Claude 3.7 Sonnet.", "strategy": {"label": "Стратегия диффа", "options": {"standard": "Стандар<PERSON>ная (один блок)", "multiBlock": "Экспериментально: Мультиблочный дифф", "unified": "Экспериментально: Унифицированный дифф"}, "descriptions": {"standard": "Стандартная стратегия применяет изменения к одному блоку кода за раз.", "unified": "Унифицированная стратегия использует несколько подходов к применению диффов и выбирает лучший.", "multiBlock": "Мультиблочная стратегия позволяет обновлять несколько блоков кода в файле за один запрос."}}, "matchPrecision": {"label": "Точность совпадения", "description": "Этот ползунок управляет точностью совпадения секций кода при применении диффов. Меньшие значения позволяют более гибкое совпадение, но увеличивают риск неверной замены. Используйте значения ниже 100% с осторожностью."}}, "todoList": {"label": "Включить инструмент списка задач", "description": "При включении Roo может создавать и управлять списками задач для отслеживания прогресса. Это помогает организовать сложные задачи в управляемые шаги."}}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "Использовать экспериментальную стратегию унифицированного диффа", "description": "Включает экспериментальную стратегию унифицированного диффа. Может уменьшить количество повторных попыток из-за ошибок модели, но может привести к неожиданному поведению или неверным правкам. Включайте только если готовы внимательно проверять все изменения."}, "SEARCH_AND_REPLACE": {"name": "Использовать экспериментальный инструмент поиска и замены", "description": "Включает экспериментальный инструмент поиска и замены, позволяя Roo заменять несколько вхождений за один запрос."}, "INSERT_BLOCK": {"name": "Использовать экспериментальный инструмент вставки контента", "description": "Включает экспериментальный инструмент вставки контента, позволяя Roo вставлять контент по номеру строки без создания диффа."}, "POWER_STEERING": {"name": "Использовать экспериментальный режим \"power steering\"", "description": "Если включено, Roo будет чаще напоминать модели детали текущего режима. Это приведёт к более строгому следованию ролям и инструкциям, но увеличит расход токенов."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Использовать экспериментальный мультиблочный инструмент диффа", "description": "Если включено, Roo будет использовать мультиблочный инструмент диффа, пытаясь обновить несколько блоков кода за один запрос."}, "CONCURRENT_FILE_READS": {"name": "Включить одновременное чтение файлов", "description": "При включении Roo может читать несколько файлов в одном запросе. При отключении Roo должен читать файлы по одному. Отключение может помочь при работе с менее производительными моделями или когда вы хотите больше контроля над доступом к файлам."}, "MARKETPLACE": {"name": "Включить Marketplace", "description": "Когда включено, вы можете устанавливать MCP и пользовательские режимы из Marketplace."}, "MULTI_FILE_APPLY_DIFF": {"name": "Включить одновременное редактирование файлов", "description": "Когда включено, Roo может редактировать несколько файлов в одном запросе. Когда отключено, Roo должен редактировать файлы по одному. Отключение этой функции может помочь при работе с менее способными моделями или когда вы хотите больше контроля над изменениями файлов."}}, "promptCaching": {"label": "Отключить кэширование промптов", "description": "Если отмечено, Roo не будет использовать кэширование промптов для этой модели."}, "temperature": {"useCustom": "Использовать пользовательскую температуру", "description": "Управляет случайностью ответов модели.", "rangeDescription": "Более высокие значения делают ответы более случайными, низкие — более детерминированными."}, "modelInfo": {"supportsImages": "Поддерживает изображения", "noImages": "Не поддерживает изображения", "supportsComputerUse": "Поддерживает использование компьютера", "noComputerUse": "Не поддерживает использование компьютера", "supportsPromptCache": "Поддерживает кэширование подсказок", "noPromptCache": "Не поддерживает кэширование подсказок", "maxOutput": "Максимум вывода", "inputPrice": "Цена за вход", "outputPrice": "Цена за вывод", "cacheReadsPrice": "Цена чтения из кэша", "cacheWritesPrice": "Цена записи в кэш", "enableStreaming": "Включить потоковую передачу", "enableR1Format": "Включить параметры модели R1", "enableR1FormatTips": "Необходимо включить при использовании моделей R1 (например, QWQ), чтобы избежать ошибок 400", "useAzure": "Использовать Azure", "azureApiVersion": "Установить версию API Azure", "gemini": {"freeRequests": "* Бесплатно до {{count}} запросов в минуту. Далее тарификация зависит от размера подсказки.", "pricingDetails": "Подробнее о ценах.", "billingEstimate": "* Счёт — приблизительный, точная стоимость зависит от размера подсказки."}}, "modelPicker": {"automaticFetch": "Расширение автоматически получает актуальный список моделей на <serviceLink>{{serviceName}}</serviceLink>. Если не уверены, что выбрать, Roo Code лучше всего работает с <defaultModelLink>{{defaultModelId}}</defaultModelLink>. Также попробуйте поискать \"free\" для бесплатных вариантов.", "label": "Модель", "searchPlaceholder": "Поиск", "noMatchFound": "Совпадений не найдено", "useCustomModel": "Использовать пользовательскую: {{modelId}}"}, "footer": {"feedback": "Если у вас есть вопросы или предложения, откройте issue на <githubLink>github.com/RooCodeInc/Roo-Code</githubLink> или присоединяйтесь к <redditLink>reddit.com/r/RooCode</redditLink> или <discordLink>discord.gg/roocode</discordLink>", "telemetry": {"label": "Разрешить анонимную отправку ошибок и статистики использования", "description": "Помогите улучшить Roo Code, отправляя анонимные данные об ошибках и использовании. Код, подсказки и личная информация не отправляются. Подробнее — в политике конфиденциальности."}, "settings": {"import": "Импорт", "export": "Экспорт", "reset": "Сбросить"}}, "thinkingBudget": {"maxTokens": "Максимум токенов", "maxThinkingTokens": "Максимум токенов на размышления"}, "validation": {"apiKey": "Вы должны указать действительный API-ключ.", "awsRegion": "Вы должны выбрать регион для использования с Amazon Bedrock.", "googleCloud": "Вы должны указать действительный Project ID и регион Google Cloud.", "modelId": "Вы должны указать действительный ID модели.", "modelSelector": "Вы должны указать действительный селектор модели.", "openAi": "Вы должны указать действительный базовый URL, API-ключ и ID модели.", "arn": {"invalidFormat": "Неверный формат ARN. Проверьте требования к формату.", "regionMismatch": "Внимание: регион в вашем ARN ({{arnRegion}}) не совпадает с выбранным регионом ({{region}}). Это может вызвать проблемы с доступом. Провайдер будет использовать регион из ARN."}, "modelAvailability": "ID модели ({{modelId}}), который вы указали, недоступен. Пожалуйста, выберите другую модель.", "providerNotAllowed": "Провайдер '{{provider}}' не разрешен вашей организацией", "modelNotAllowed": "Модель '{{model}}' не разрешена для провайдера '{{provider}}' вашей организацией", "profileInvalid": "Этот профиль содержит провайдера или модель, которые не разрешены вашей организацией"}, "placeholders": {"apiKey": "Введите API-ключ...", "profileName": "Введите имя профиля", "accessKey": "Введите Access Key...", "secretKey": "Введите Secret Key...", "sessionToken": "Введите Session Token...", "credentialsJson": "Введите Credentials JSON...", "keyFilePath": "Введите путь к ключу...", "projectId": "Введите Project ID...", "customArn": "Введите ARN (например, arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Введите базовый URL...", "modelId": {"lmStudio": "например, meta-llama-3.1-8b-instruct", "lmStudioDraft": "например, lmstudio-community/llama-3.2-1b-instruct", "ollama": "например, llama3.1"}, "numbers": {"maxTokens": "например, 4096", "contextWindow": "например, 128000", "inputPrice": "например, 0.0001", "outputPrice": "например, 0.0002", "cacheWritePrice": "например, 0.00005"}}, "defaults": {"ollamaUrl": "По умолчанию: http://localhost:11434", "lmStudioUrl": "По умолчанию: http://localhost:1234", "geminiUrl": "По умолчанию: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "Пользовательский ARN", "useCustomArn": "Использовать пользовательский ARN..."}, "includeMaxOutputTokens": "Включить максимальные выходные токены", "includeMaxOutputTokensDescription": "Отправлять параметр максимальных выходных токенов в API-запросах. Некоторые провайдеры могут не поддерживать это."}