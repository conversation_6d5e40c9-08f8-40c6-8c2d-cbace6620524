{"extension.displayName": "Roo Code (prev. Roo Cline)", "extension.description": "A whole dev team of AI agents in your editor.", "views.contextMenu.label": "Roo Code", "views.terminalMenu.label": "Roo Code", "views.activitybar.title": "Roo Code", "views.sidebar.name": "Roo Code", "command.newTask.title": "New Task", "command.mcpServers.title": "MCP Servers", "command.prompts.title": "Modes", "command.history.title": "History", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "Open in Editor", "command.settings.title": "Settings", "command.documentation.title": "Documentation", "command.openInNewTab.title": "Open In New Tab", "command.explainCode.title": "Explain Code", "command.fixCode.title": "Fix Code", "command.improveCode.title": "Improve Code", "command.addToContext.title": "Add To Context", "command.focusInput.title": "Focus Input Field", "command.setCustomStoragePath.title": "Set Custom Storage Path", "command.importSettings.title": "Import Settings", "command.terminal.addToContext.title": "Add Terminal Content to Context", "command.terminal.fixCommand.title": "Fix This Command", "command.terminal.explainCommand.title": "Explain This Command", "command.acceptInput.title": "Accept Input/Suggestion", "configuration.title": "Roo Code", "commands.allowedCommands.description": "Commands that can be auto-executed when 'Always approve execute operations' is enabled", "commands.deniedCommands.description": "Command prefixes that will be automatically denied without asking for approval. In case of conflicts with allowed commands, the longest prefix match takes precedence. Add * to deny all commands.", "commands.commandExecutionTimeout.description": "Maximum time in seconds to wait for command execution to complete before timing out (0 = no timeout, 1-600s, default: 0s)", "commands.commandTimeoutAllowlist.description": "Command prefixes that are excluded from the command execution timeout. Commands matching these prefixes will run without timeout restrictions.", "commands.preventCompletionWithOpenTodos.description": "Prevent task completion when there are incomplete todos in the todo list", "settings.vsCodeLmModelSelector.description": "Settings for VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "The vendor of the language model (e.g. copilot)", "settings.vsCodeLmModelSelector.family.description": "The family of the language model (e.g. gpt-4)", "settings.customStoragePath.description": "Custom storage path. Leave empty to use the default location. Supports absolute paths (e.g. 'D:\\RooCodeStorage')", "settings.enableCodeActions.description": "Enable Roo Code quick fixes", "settings.autoImportSettingsPath.description": "Path to a RooCode configuration file to automatically import on extension startup. Supports absolute paths and paths relative to the home directory (e.g. '~/Documents/roo-code-settings.json'). Leave empty to disable auto-import."}